import { isCelebrateError } from "celebrate";

const HandleErrorMessage = async (err: any, req: any, res: any, next: any) => {
  try {
    // Check for the specific "Invalid value" error format we're seeing
    if (err.message && typeof err.message === 'string' && err.message.startsWith("Invalid value")) {
      return res.status(400).json({
        status: false,
        message: "Validation error: " + err.message,
        errorType: "VALIDATION_ERROR"
      });
    }

    if (isCelebrateError(err)) {
      let errorDetails: any;

      // Check different sources of validation errors
      if (err.details.get("body")) {
        errorDetails = err.details.get("body");
      } else if (err.details.get("query")) {
        errorDetails = err.details.get("query");
      } else if (err.details.get("params")) {
        errorDetails = err.details.get("params");
      } else if (err.details.get("headers")) {
        errorDetails = err.details.get("headers");
      }

      if (
        errorDetails &&
        errorDetails.details &&
        errorDetails.details.length > 0
      ) {
        // Get the first error for the main message
        const firstError = errorDetails.details[0];
        const fieldPath = firstError.path ? firstError.path.join(".") : "field";



        // Create user-friendly main message
        let mainMessage = "Validation failed";

        // Customize main message based on error type
        if (firstError.type === "any.required") {
          mainMessage = `${fieldPath} is required`;
        } else if (firstError.type === "string.empty") {
          mainMessage = `${fieldPath} cannot be empty`;
        } else if (firstError.type === "string.max") {
          mainMessage = `${fieldPath} must not exceed ${firstError.context?.limit} characters`;
        } else if (firstError.type === "string.min") {
          mainMessage = `${fieldPath} must be at least ${firstError.context?.limit} characters`;
        } else if (firstError.type === "number.min") {
          mainMessage = `${fieldPath} must be at least ${firstError.context?.limit}`;
        } else if (firstError.type === "number.max") {
          mainMessage = `${fieldPath} must not exceed ${firstError.context?.limit}`;
        } else if (firstError.type === "any.only") {
          // Try different possible property names for valid options
          let validOptions = "valid options";

          if (firstError.context?.valids && Array.isArray(firstError.context.valids)) {
            validOptions = firstError.context.valids.join(", ");
          } else if (firstError.context?.valid && Array.isArray(firstError.context.valid)) {
            validOptions = firstError.context.valid.join(", ");
          } else if (firstError.context?.allowedValues && Array.isArray(firstError.context.allowedValues)) {
            validOptions = firstError.context.allowedValues.join(", ");
          } else {
            // If we can't find the valid options, extract them from the original message
            const originalMessage = firstError.message || "";
            // Try different patterns to extract valid options
            let match = originalMessage.match(/must be one of \[(.*?)\]/);
            if (!match) {
              match = originalMessage.match(/must be one of (.*)/);
            }
            if (match && match[1]) {
              validOptions = match[1].replace(/"/g, "").replace(/\[|\]/g, "");
            } else {
              // Last resort: use the original message but clean it up
              validOptions = originalMessage.replace(/.*must be one of\s*/i, "").replace(/"/g, "");
            }
          }

          mainMessage = `${fieldPath} must be one of [${validOptions}]`;
        } else if (firstError.type === "string.pattern.base") {
          mainMessage = `${fieldPath} format is invalid`;
        } else if (firstError.type === "number.base") {
          mainMessage = `${fieldPath} must be a valid number`;
        } else if (firstError.type === "boolean.base") {
          mainMessage = `${fieldPath} must be true or false`;
        } else if (firstError.type === "alternatives.match") {
          mainMessage = firstError.message || `${fieldPath} has invalid format`;
        } else if (firstError.type === "object.unknown") {
          mainMessage = `${fieldPath} is not allowed`;
        } else {
          // For any other error type, try to include field name if not already present
          const cleanMessage = firstError.message
            .replace(/"/g, "") // Remove quotes
            .replace(/\\/g, "") // Remove backslashes
            .trim();

          // If the message doesn't already contain the field name, prepend it
          if (!cleanMessage.includes(fieldPath) && fieldPath !== "field") {
            mainMessage = `${fieldPath} ${cleanMessage}`;
          } else {
            mainMessage = cleanMessage;
          }
        }

        return res.status(400).json({
          status: false,
          message: mainMessage,
        });
      }

      // Fallback for celebrate errors without detailed information
      return res.status(400).json({
        status: false,
        message: "Invalid request data",
      });
    }

    // Check if this is the specific "Invalid value" error we're seeing
    if (err.message && err.message.startsWith("Invalid value")) {
      return res.status(400).json({
        status: false,
        message: err.message,
        errorType: "VALIDATION_ERROR"
      });
    }

    // Pass to the next error handler
    next(err);
  } catch (e: any) {
    console.error("Error in validation middleware:", e);
    return res.status(400).json({
      status: false,
      message: "Validation error occurred",
    });
  }
};

export default HandleErrorMessage;
