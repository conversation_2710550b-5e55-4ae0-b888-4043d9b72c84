// Quick test to verify date filtering logic
const testDateRange = (dateRange) => {
  const endDate = new Date();
  const startDate = new Date();

  switch (dateRange) {
    case "last_year": {
      const currentDate = new Date();
      const lastYear = currentDate.getFullYear() - 1;

      // Set start date to beginning of last calendar year
      startDate.setFullYear(lastYear, 0, 1); // January 1st of last year
      startDate.setHours(0, 0, 0, 0);

      // Set end date to end of last calendar year
      endDate.setFullYear(lastYear, 11, 31); // December 31st of last year
      endDate.setHours(23, 59, 59, 999);
      break;
    }
    case "last_30_days":
      startDate.setDate(endDate.getDate() - 30);
      break;
  }

  return { startDate, endDate };
};

// Test the date ranges
console.log("=== Date Range Testing ===");
console.log("Current date:", new Date().toISOString());

const lastYear = testDateRange("last_year");
console.log("Last Year Range:");
console.log("  Start:", lastYear.startDate.toISOString());
console.log("  End:", lastYear.endDate.toISOString());

const last30Days = testDateRange("last_30_days");
console.log("Last 30 Days Range:");
console.log("  Start:", last30Days.startDate.toISOString());
console.log("  End:", last30Days.endDate.toISOString());

// Verify that last_year is actually from 2024
const expectedYear = new Date().getFullYear() - 1;
console.log(`\nExpected year for last_year: ${expectedYear}`);
console.log(`Actual start year: ${lastYear.startDate.getFullYear()}`);
console.log(`Actual end year: ${lastYear.endDate.getFullYear()}`);
console.log(`Date filtering is correct: ${lastYear.startDate.getFullYear() === expectedYear && lastYear.endDate.getFullYear() === expectedYear}`);